nguyên tắt làm việc AI.
1. <PERSON><PERSON><PERSON><PERSON> kế xong cấu trúc thư mục
2. <PERSON><PERSON><PERSON> truc DB cơ bản dự án
3. <PERSON><PERSON>n hỏi AI cần file nào trước khi code 1 chức năng.
4. <PERSON><PERSON> cấp files và nội dung: "Các file đang dùng"

Bắt đầu:
Tôi cần bạn kiểm tra mã nguồn của tôi, project_structure.txt là cấu trúc thư mực dự án. 
Cùng một source code, tôi đang chạy 2 service
- Servive  APISportsGame( main.ts): dùng để handle các HTTP request.
- service  AutoUpdateSportsGame ( worker.ts): tự động cập nhật data.
Kiềm tra cách import cảu tôi có tối ưu hay không?


Tạo folder tên: LogWorking, để lưu các tóm tắt nhừng gì đã hoàn thành, theo format
STT_gio_phut_ngay_thang_nam_tenchucnang.md và tạo doucment nếu có.
DEPLOYMENT_GUIDE.md
Read.me nội dung LogWorking - Tóm tắt công việc đã hoàn thành
Têu cầu AI tạo file rule để báo theo nguyên tắt code ( .augment-rules.md)

Tôi cần bạn luôn nhớ cấu trúc dự án trong lúc phát triển. Tôi đề xuất:
1. LogWorking/README.md: Complete project overview
2. Luôn cập nhật các file mỗi khi hoàn thành một chức năng/module, để dễ theo dõi và review lại.

VD promt:
Đây là thư mục root dự án FE CMS  ( tên APISportsGamev2-FECMS), sử dụng api từ service tên APISportsGame. Đảm bảo AI luôn nhớ cấu trúc dự án, quá trình thay đổi và cải tiến, tôi đề xuất:
- Tạo file Rule cho phát triển code tên:  .augment-rules.md .
- Tạo thư mục LogWorking lưu tóm tắt từng chức năng đã hoàn thành, theo format 
số thứ tự_gio_phut_ngay_thang_nam_tenchucnang.md, các file này giúp AI review và nhớ những gì mình làm, tôi tiện theo dõi. Tạo doucment nếu có: DEPLOYMENT_GUIDE.md, README.md ghi Complete project overview.
- Theo dõi cấu trúc mã nguồn tại : project_structure.txt

Project này là CMS ( tập trung các tính năng quản lý thể thao): Quản lý user system, quản lý user register, quản lý giải đấu, quản lý trận đấu), page login. Nếu bạn đã hiểu, chúng ta bắt đầu phân tích và thiết kế cấu trúc dự án  chi tiết theo module hóa. Bạn có câu hỏi nào cho tôi? 

Sau khi trả lời câu hỏi, yêu cầu AI lập kế hoạch ( hoặc lặp kế hoạch sau khi đọc document API swagger)