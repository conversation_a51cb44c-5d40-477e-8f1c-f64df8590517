import { IsInt, IsString, IsNotEmpty, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class CreateBroadcastLinkDto {
    @ApiProperty({
        description: 'Fixture external ID',
        example: 868847
    })
    @Type(() => Number)
    @IsInt()
    @IsNotEmpty()
    fixtureId: number;

    @ApiProperty({
        description: 'Name of the broadcast link',
        example: 'YouTube Live Stream'
    })
    @IsString()
    @IsNotEmpty()
    linkName: string;

    @ApiProperty({
        description: 'URL of the broadcast link',
        example: 'https://youtube.com/watch?v=abc123'
    })
    @IsString()
    @IsNotEmpty()
    linkUrl: string;

    @ApiProperty({
        description: 'Comment or description for the link',
        example: 'Official HD stream with English commentary'
    })
    @IsString()
    @IsNotEmpty()
    linkComment: string;

    // addedBy will be set automatically from current user
    addedBy?: number;
}
export class UpdateBroadcastLinkDto {
    @ApiProperty({
        description: 'Name of the broadcast link',
        example: 'Updated YouTube Live Stream',
        required: false
    })
    @IsString()
    @IsOptional()
    linkName?: string;

    @ApiProperty({
        description: 'URL of the broadcast link',
        example: 'https://youtube.com/watch?v=updated123',
        required: false
    })
    @IsString()
    @IsOptional()
    linkUrl?: string;

    @ApiProperty({
        description: 'Comment or description for the link',
        example: 'Updated HD stream with multiple languages',
        required: false
    })
    @IsString()
    @IsOptional()
    linkComment?: string;

    // addedBy should not be updated
}

export class BroadcastLinkResponseDto {
    @ApiProperty({
        description: 'Broadcast link ID',
        example: 1
    })
    id: number;

    @ApiProperty({
        description: 'Fixture external ID',
        example: 868847
    })
    fixtureId: number;

    @ApiProperty({
        description: 'Name of the broadcast link',
        example: 'YouTube Live Stream'
    })
    linkName: string;

    @ApiProperty({
        description: 'Comment or description for the link',
        example: 'Official HD stream with English commentary'
    })
    linkComment: string;

    @ApiProperty({
        description: 'URL of the broadcast link',
        example: 'https://youtube.com/watch?v=abc123'
    })
    linkUrl: string;

    @ApiProperty({
        description: 'ID of user who added this link',
        example: 1
    })
    addedBy: number;

    @ApiProperty({
        description: 'Creation timestamp',
        example: '2024-05-24T10:30:00.000Z'
    })
    createdAt: string;

    @ApiProperty({
        description: 'Last update timestamp',
        example: '2024-05-24T10:30:00.000Z'
    })
    updatedAt: string;
}