import { Controller, Post, Get, Body, Param, Patch, Delete, UseGuards, ParseIntPipe } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse, ApiParam, ApiBody } from '@nestjs/swagger';
import { BroadcastLinkService } from './broadcast-link.service';
import { CreateBroadcastLinkDto, BroadcastLinkResponseDto, UpdateBroadcastLinkDto } from './broadcast-link.dto';
import { SystemJwtAuthGuard } from '../auth/system/guards/system-jwt-auth.guard';
import { SystemRolesGuard } from '../auth/system/guards/system-roles.guard';
import { GetCurrentUser } from '../auth/core/decorators/auth.decorators';
import { SystemUser } from '../auth/system/entities/system-user.entity';

@ApiTags('Broadcast Links')
@ApiBearerAuth()
@UseGuards(SystemJwtAuthGuard, SystemRolesGuard)
@Controller('broadcast-links')
export class BroadcastLinkController {
    constructor(private readonly broadcastLinkService: BroadcastLinkService) { }

    @ApiOperation({
        summary: 'Create Broadcast Link (SystemUser Only)',
        description: `
        Create a new broadcast link for a fixture.

        **Access:** SystemUser only (Admin/Editor/Moderator)
        **Features:**
        - Automatic user tracking (addedBy)
        - Fixture validation
        - URL validation
        - Audit logging

        **Permissions:**
        - Admin: Can create for any fixture
        - Editor: Can create for any fixture
        - Moderator: Can create for any fixture
        `
    })
    @ApiBody({ type: CreateBroadcastLinkDto })
    @ApiResponse({
        status: 201,
        description: 'Broadcast link created successfully',
        type: BroadcastLinkResponseDto
    })
    @ApiResponse({
        status: 403,
        description: 'SystemUser access required'
    })
    @ApiResponse({
        status: 404,
        description: 'Fixture not found'
    })
    @Post()
    async createBroadcastLink(
        @Body() createBroadcastLinkDto: CreateBroadcastLinkDto,
        @GetCurrentUser() currentUser: SystemUser
    ): Promise<{ data: BroadcastLinkResponseDto; status: number }> {
        // Set addedBy to current user ID
        const createDto = {
            ...createBroadcastLinkDto,
            addedBy: currentUser.id
        };

        const broadcastLink = await this.broadcastLinkService.createBroadcastLink(createDto);
        return { data: broadcastLink, status: 201 };
    }

    @ApiOperation({
        summary: 'Get Broadcast Links by Fixture',
        description: `
        Get broadcast links for a specific fixture with role-based filtering.

        **Permissions:**
        - Admin/Moderator: See all broadcast links for the fixture
        - Editor: See only broadcast links they created

        **Features:**
        - Role-based data filtering
        - Fixture validation
        - Ownership-based access control
        `
    })
    @ApiParam({ name: 'fixtureId', type: 'number', description: 'Fixture external ID', example: 868847 })
    @ApiResponse({
        status: 200,
        description: 'Broadcast links retrieved successfully',
        type: [BroadcastLinkResponseDto]
    })
    @ApiResponse({
        status: 404,
        description: 'Fixture not found'
    })
    @Get('fixture/:fixtureId')
    async getBroadcastLinksByFixtureId(
        @Param('fixtureId', ParseIntPipe) fixtureId: number,
        @GetCurrentUser() currentUser: SystemUser
    ): Promise<{ data: BroadcastLinkResponseDto[]; status: number }> {
        const broadcastLinks = await this.broadcastLinkService.getBroadcastLinksByFixtureId(fixtureId, currentUser);
        return { data: broadcastLinks, status: 200 };
    }

    @ApiOperation({
        summary: 'Update Broadcast Link',
        description: `
        Update a broadcast link with ownership-based permissions.

        **Permissions:**
        - Admin/Moderator: Can update any broadcast link
        - Editor: Can only update broadcast links they created

        **Features:**
        - Ownership validation for editors
        - URL validation
        - Audit logging
        `
    })
    @ApiParam({ name: 'id', type: 'number', description: 'Broadcast link ID', example: 1 })
    @ApiBody({ type: UpdateBroadcastLinkDto })
    @ApiResponse({
        status: 200,
        description: 'Broadcast link updated successfully',
        type: BroadcastLinkResponseDto
    })
    @ApiResponse({
        status: 403,
        description: 'Insufficient permissions'
    })
    @ApiResponse({
        status: 404,
        description: 'Broadcast link not found'
    })
    @Patch(':id')
    async updateBroadcastLink(
        @Param('id', ParseIntPipe) id: number,
        @Body() updateBroadcastLinkDto: UpdateBroadcastLinkDto,
        @GetCurrentUser() currentUser: SystemUser
    ): Promise<{ data: BroadcastLinkResponseDto; status: number }> {
        const broadcastLink = await this.broadcastLinkService.updateBroadcastLink(id, updateBroadcastLinkDto, currentUser);
        return { data: broadcastLink, status: 200 };
    }

    @ApiOperation({
        summary: 'Delete Broadcast Link',
        description: `
        Delete a broadcast link with ownership-based permissions.

        **Permissions:**
        - Admin/Moderator: Can delete any broadcast link
        - Editor: Can only delete broadcast links they created

        **Features:**
        - Ownership validation for editors
        - Soft delete with audit logging
        - Permission enforcement
        `
    })
    @ApiParam({ name: 'id', type: 'number', description: 'Broadcast link ID', example: 1 })
    @ApiResponse({
        status: 204,
        description: 'Broadcast link deleted successfully'
    })
    @ApiResponse({
        status: 403,
        description: 'Insufficient permissions'
    })
    @ApiResponse({
        status: 404,
        description: 'Broadcast link not found'
    })
    @Delete(':id')
    async deleteBroadcastLink(
        @Param('id', ParseIntPipe) id: number,
        @GetCurrentUser() currentUser: SystemUser
    ): Promise<{ status: number }> {
        await this.broadcastLinkService.deleteBroadcastLink(id, currentUser);
        return { status: 204 };
    }
}