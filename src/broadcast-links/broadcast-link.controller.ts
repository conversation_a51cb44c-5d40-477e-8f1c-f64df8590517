import { Controller, Post, Get, Body, Param, Patch, Delete, UseGuards, ParseIntPipe } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse, ApiParam, ApiBody } from '@nestjs/swagger';
import { BroadcastLinkService } from './broadcast-link.service';
import { CreateBroadcastLinkDto, BroadcastLinkResponseDto, UpdateBroadcastLinkDto, PublicBroadcastLinkResponseDto } from './broadcast-link.dto';
import { SystemJwtAuthGuard } from '../auth/system/guards/system-jwt-auth.guard';
import { SystemRolesGuard } from '../auth/system/guards/system-roles.guard';
import { GetCurrentUser } from '../auth/core/decorators/auth.decorators';
import { SystemUser } from '../auth/system/entities/system-user.entity';

@ApiTags('Broadcast Links')
@ApiBearerAuth('bearer')
@UseGuards(SystemJwtAuthGuard, SystemRolesGuard)
@Controller('broadcast-links')
export class BroadcastLinkController {
    constructor(private readonly broadcastLinkService: BroadcastLinkService) { }

    @ApiOperation({
        summary: 'Create Broadcast Link (SystemUser Only)',
        description: `
        Create a new broadcast link for a fixture with role-based permissions.

        **🔒 AUTHENTICATION REQUIRED:**
        SystemUser authentication required (Admin/Moderator/Editor roles accepted).

        **Quick Auth Setup in Swagger UI:**
        1. Login: POST /system-auth/login
        2. Copy "accessToken" from response
        3. Click "Authorize" button (🔓) at top of page
        4. Enter: Bearer YOUR_ACCESS_TOKEN
        5. Click "Authorize" and "Close"

        **Test Credentials (All roles can create):**
        - Admin: {"username": "admin", "password": "admin123456"}
        - Editor: {"username": "editor1", "password": "editor123456"}
        - Moderator: {"username": "moderator1", "password": "moderator123456"}

        **Role Permissions:**
        - **Admin**: Can create broadcast links for any fixture
        - **Moderator**: Can create broadcast links for any fixture
        - **Editor**: Can create broadcast links for any fixture

        **Note:** All SystemUser roles can create BroadcastLinks, but viewing/editing permissions differ:
        - Admin/Moderator: Can view/edit/delete all BroadcastLinks
        - Editor: Can only view/edit/delete own BroadcastLinks

        **Example Request:**
        \`\`\`
        POST /broadcast-links HTTP/1.1
        Authorization: Bearer YOUR_ACCESS_TOKEN
        Content-Type: application/json

        {
          "fixtureId": 1274453,
          "linkName": "Live Stream HD",
          "linkUrl": "https://youtube.com/watch?v=example123",
          "linkComment": "Official broadcast link"
        }
        \`\`\`

        **Security:**
        - addedBy automatically set to current user ID
        - Fixture existence validation
        - URL format validation
        - Input sanitization
        `
    })
    @ApiBody({ type: CreateBroadcastLinkDto })
    @ApiResponse({
        status: 201,
        description: 'Broadcast link created successfully',
        type: BroadcastLinkResponseDto
    })
    @ApiResponse({
        status: 403,
        description: 'SystemUser access required'
    })
    @ApiResponse({
        status: 404,
        description: 'Fixture not found'
    })
    @Post()
    async createBroadcastLink(
        @Body() createBroadcastLinkDto: CreateBroadcastLinkDto,
        @GetCurrentUser() currentUser: SystemUser
    ): Promise<{ data: BroadcastLinkResponseDto; status: number }> {
        // Set addedBy to current user ID
        const createDto = {
            ...createBroadcastLinkDto,
            addedBy: currentUser.id
        };

        const broadcastLink = await this.broadcastLinkService.createBroadcastLink(createDto);
        return { data: broadcastLink, status: 201 };
    }

    @ApiOperation({
        summary: 'Get Broadcast Links by Fixture',
        description: `
        Get broadcast links for a specific fixture with role-based data filtering.

        **Role-Based Permissions:**
        - **Admin**: See all broadcast links for the fixture (full access)
        - **Moderator**: See all broadcast links for the fixture (full access)
        - **Editor**: See only broadcast links they created (own only)

        **Features:**
        - Automatic role-based data filtering
        - Fixture existence validation
        - Ownership-based access control
        - Comprehensive audit logging

        **Data Filtering Logic:**
        - Admin/Moderator: Query returns all BroadcastLinks for fixture
        - Editor: Query filtered by addedBy = currentUser.id

        **Security:**
        - No sensitive data exposure
        - Role-based query filtering
        - User context validation
        `
    })
    @ApiParam({ name: 'fixtureId', type: 'number', description: 'Fixture external ID', example: 868847 })
    @ApiResponse({
        status: 200,
        description: 'Broadcast links retrieved successfully',
        type: [BroadcastLinkResponseDto]
    })
    @ApiResponse({
        status: 404,
        description: 'Fixture not found'
    })
    @Get('fixture/:fixtureId')
    async getBroadcastLinksByFixtureId(
        @Param('fixtureId', ParseIntPipe) fixtureId: number,
        @GetCurrentUser() currentUser: SystemUser
    ): Promise<{ data: BroadcastLinkResponseDto[]; status: number }> {
        const broadcastLinks = await this.broadcastLinkService.getBroadcastLinksByFixtureId(fixtureId, currentUser);
        return { data: broadcastLinks, status: 200 };
    }

    @ApiOperation({
        summary: 'Update Broadcast Link',
        description: `
        Update a broadcast link with role-based ownership permissions.

        **Role-Based Permissions:**
        - **Admin**: Can update any broadcast link (full access)
        - **Moderator**: Can update any broadcast link (full access)
        - **Editor**: Can only update broadcast links they created (own only)

        **Features:**
        - Automatic ownership validation for editors
        - URL format validation and sanitization
        - Comprehensive audit logging
        - addedBy field preservation (cannot be changed)

        **Security:**
        - Ownership checks: Editor can only modify own BroadcastLinks
        - Permission validation before any updates
        - Input sanitization and validation
        - Audit trail for all modifications

        **Validation:**
        - BroadcastLink existence check
        - User permission verification
        - URL format validation
        - Input data sanitization
        `
    })
    @ApiParam({ name: 'id', type: 'number', description: 'Broadcast link ID', example: 1 })
    @ApiBody({ type: UpdateBroadcastLinkDto })
    @ApiResponse({
        status: 200,
        description: 'Broadcast link updated successfully',
        type: BroadcastLinkResponseDto
    })
    @ApiResponse({
        status: 403,
        description: 'Insufficient permissions'
    })
    @ApiResponse({
        status: 404,
        description: 'Broadcast link not found'
    })
    @Patch(':id')
    async updateBroadcastLink(
        @Param('id', ParseIntPipe) id: number,
        @Body() updateBroadcastLinkDto: UpdateBroadcastLinkDto,
        @GetCurrentUser() currentUser: SystemUser
    ): Promise<{ data: BroadcastLinkResponseDto; status: number }> {
        const broadcastLink = await this.broadcastLinkService.updateBroadcastLink(id, updateBroadcastLinkDto, currentUser);
        return { data: broadcastLink, status: 200 };
    }

    @ApiOperation({
        summary: 'Delete Broadcast Link',
        description: `
        Delete a broadcast link with role-based ownership permissions.

        **Role-Based Permissions:**
        - **Admin**: Can delete any broadcast link (full access)
        - **Moderator**: Can delete any broadcast link (full access)
        - **Editor**: Can only delete broadcast links they created (own only)

        **Features:**
        - Automatic ownership validation for editors
        - Hard delete with comprehensive audit logging
        - Permission enforcement before deletion
        - Complete removal from database

        **Security:**
        - Ownership checks: Editor can only delete own BroadcastLinks
        - Permission validation before any deletion
        - Comprehensive audit trail
        - User context validation

        **Validation:**
        - BroadcastLink existence check
        - User permission verification
        - Ownership validation for editors
        - Audit logging before deletion
        `
    })
    @ApiParam({ name: 'id', type: 'number', description: 'Broadcast link ID', example: 1 })
    @ApiResponse({
        status: 204,
        description: 'Broadcast link deleted successfully'
    })
    @ApiResponse({
        status: 403,
        description: 'Insufficient permissions'
    })
    @ApiResponse({
        status: 404,
        description: 'Broadcast link not found'
    })
    @Delete(':id')
    async deleteBroadcastLink(
        @Param('id', ParseIntPipe) id: number,
        @GetCurrentUser() currentUser: SystemUser
    ): Promise<{ status: number }> {
        await this.broadcastLinkService.deleteBroadcastLink(id, currentUser);
        return { status: 204 };
    }
}

// Public endpoints for end users (no authentication required)
@ApiTags('Public - Broadcast Links')
@Controller('public/broadcast-links')
export class PublicBroadcastLinkController {
    constructor(private readonly broadcastLinkService: BroadcastLinkService) { }

    @ApiOperation({
        summary: 'Get Broadcast Links for Fixture (Public)',
        description: `
        Get all available broadcast links for a specific fixture. This is a public endpoint for end users.

        **🌐 PUBLIC ENDPOINT:**
        No authentication required. This endpoint is designed for end users to get streaming links.

        **Features:**
        - Public access for all users
        - Returns all available broadcast links for a fixture
        - No sensitive information exposed
        - Optimized for end user consumption

        **Use Cases:**
        - Mobile apps showing streaming options
        - Website displaying available streams
        - Third-party integrations
        - End user applications

        **Response Format:**
        Returns simplified broadcast link information without internal metadata.

        **Example Usage:**
        \`\`\`
        GET /public/broadcast-links/fixture/868847 HTTP/1.1
        Host: localhost:3000

        # No authentication headers needed
        \`\`\`

        **Rate Limiting:**
        - No authentication required
        - Standard rate limiting applies
        - Designed for high-volume public access
        `
    })
    @ApiParam({
        name: 'fixtureId',
        type: 'number',
        description: 'Fixture external ID',
        example: 868847
    })
    @ApiResponse({
        status: 200,
        description: 'Broadcast links retrieved successfully',
        type: [PublicBroadcastLinkResponseDto],
        example: [
            {
                id: 1,
                linkName: 'YouTube Live Stream',
                linkUrl: 'https://youtube.com/watch?v=abc123',
                linkComment: 'Official HD stream with English commentary'
            },
            {
                id: 2,
                linkName: 'Twitch Stream',
                linkUrl: 'https://twitch.tv/example',
                linkComment: 'Alternative stream with multiple languages'
            }
        ]
    })
    @ApiResponse({
        status: 404,
        description: 'Fixture not found',
        example: {
            message: 'Fixture with externalId 868847 not found',
            error: 'Not Found',
            statusCode: 404
        }
    })
    @ApiResponse({
        status: 200,
        description: 'No broadcast links available (empty array)',
        example: []
    })
    @Get('fixture/:fixtureId')
    async getPublicBroadcastLinksByFixtureId(
        @Param('fixtureId', ParseIntPipe) fixtureId: number
    ): Promise<{ data: PublicBroadcastLinkResponseDto[]; status: number }> {
        const broadcastLinks = await this.broadcastLinkService.getPublicBroadcastLinksByFixtureId(fixtureId);
        return { data: broadcastLinks, status: 200 };
    }
}