import { Injectable, NotFoundException, Logger, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BroadcastLink } from './broadcast-link.entity';
import { Fixture } from '../sports/football/models/fixture.entity';
import { CreateBroadcastLinkDto, BroadcastLinkResponseDto, UpdateBroadcastLinkDto, PublicBroadcastLinkResponseDto } from './broadcast-link.dto';
import { isURL } from 'class-validator';
import { SystemUser } from '../auth/system/entities/system-user.entity';
import { SystemRole } from '../auth/core/types/auth.types';

@Injectable()
export class BroadcastLinkService {
    private readonly logger = new Logger(BroadcastLinkService.name);

    constructor(
        @InjectRepository(BroadcastLink)
        private readonly broadcastLinkRepository: Repository<BroadcastLink>,
        @InjectRepository(Fixture)
        private readonly fixtureRepository: Repository<Fixture>,
    ) { }

    /**
     * Create a new broadcast link
     * @param createBroadcastLinkDto - Broadcast link data
     * @returns Created broadcast link
     */
    async createBroadcastLink(createBroadcastLinkDto: CreateBroadcastLinkDto): Promise<BroadcastLinkResponseDto> {
        // Validate fixtureId
        const fixture = await this.fixtureRepository.findOneBy({ externalId: createBroadcastLinkDto.fixtureId });
        if (!fixture) {
            throw new NotFoundException(`Fixture with externalId ${createBroadcastLinkDto.fixtureId} not found`);
        }

        // Validate linkUrl
        if (!isURL(createBroadcastLinkDto.linkUrl)) {
            throw new BadRequestException(`Invalid URL: ${createBroadcastLinkDto.linkUrl}`);
        }

        // Create broadcast link
        const broadcastLink = new BroadcastLink();
        broadcastLink.fixtureId = createBroadcastLinkDto.fixtureId;
        broadcastLink.linkName = createBroadcastLinkDto.linkName;
        broadcastLink.linkUrl = createBroadcastLinkDto.linkUrl;
        broadcastLink.addedBy = createBroadcastLinkDto.addedBy!; // addedBy is set by controller
        broadcastLink.linkComment = createBroadcastLinkDto.linkComment;

        try {
            const savedBroadcastLink = await this.broadcastLinkRepository.save(broadcastLink);
            this.logger.debug(`Created broadcast link with id ${savedBroadcastLink.id} for fixture ${createBroadcastLinkDto.fixtureId}`);
            return this.mapToResponseDto(savedBroadcastLink);
        } catch (error) {
            this.logger.error(`Failed to create broadcast link: ${error.message}`);
            throw new BadRequestException(`Failed to create broadcast link: ${error.message}`);
        }
    }

    /**
     * Update an existing broadcast link with permission checks
     * @param id - Broadcast link ID
     * @param updateBroadcastLinkDto - Broadcast link data to update
     * @param currentUser - Current system user
     * @returns Updated broadcast link
     */
    async updateBroadcastLink(id: number, updateBroadcastLinkDto: UpdateBroadcastLinkDto, currentUser: SystemUser): Promise<BroadcastLinkResponseDto> {
        // Find existing broadcast link
        const broadcastLink = await this.broadcastLinkRepository.findOneBy({ id });
        if (!broadcastLink) {
            throw new NotFoundException(`Broadcast link with id ${id} not found`);
        }

        // Check permissions
        if (!this.canUserModifyBroadcastLink(currentUser, broadcastLink)) {
            throw new ForbiddenException('You can only modify broadcast links you created');
        }

        // Validate linkUrl if provided
        if (updateBroadcastLinkDto.linkUrl && !isURL(updateBroadcastLinkDto.linkUrl)) {
            throw new BadRequestException(`Invalid URL: ${updateBroadcastLinkDto.linkUrl}`);
        }

        // Update fields if provided
        if (updateBroadcastLinkDto.linkName) {
            broadcastLink.linkName = updateBroadcastLinkDto.linkName;
        }
        if (updateBroadcastLinkDto.linkUrl) {
            broadcastLink.linkUrl = updateBroadcastLinkDto.linkUrl;
        }
        if (updateBroadcastLinkDto.linkComment) {
            broadcastLink.linkComment = updateBroadcastLinkDto.linkComment;
        }
        // Don't allow updating addedBy

        try {
            const savedBroadcastLink = await this.broadcastLinkRepository.save(broadcastLink);
            this.logger.debug(`Updated broadcast link with id ${savedBroadcastLink.id} by user ${currentUser.username}`);
            return this.mapToResponseDto(savedBroadcastLink);
        } catch (error) {
            this.logger.error(`Failed to update broadcast link: ${error.message}`);
            throw new BadRequestException(`Failed to update broadcast link: ${error.message}`);
        }
    }

    /**
     * Delete a broadcast link with permission checks
     * @param id - Broadcast link ID
     * @param currentUser - Current system user
     */
    async deleteBroadcastLink(id: number, currentUser: SystemUser): Promise<void> {
        // Find existing broadcast link
        const broadcastLink = await this.broadcastLinkRepository.findOneBy({ id });
        if (!broadcastLink) {
            throw new NotFoundException(`Broadcast link with id ${id} not found`);
        }

        // Check permissions
        if (!this.canUserModifyBroadcastLink(currentUser, broadcastLink)) {
            throw new ForbiddenException('You can only delete broadcast links you created');
        }

        try {
            await this.broadcastLinkRepository.delete(id);
            this.logger.debug(`Deleted broadcast link with id ${id} by user ${currentUser.username}`);
        } catch (error) {
            this.logger.error(`Failed to delete broadcast link: ${error.message}`);
            throw new BadRequestException(`Failed to delete broadcast link: ${error.message}`);
        }
    }
    /**
     * Get broadcast links for a fixture with role-based filtering
     * @param fixtureId - Fixture external ID
     * @param currentUser - Current system user
     * @returns List of broadcast links
     */
    async getBroadcastLinksByFixtureId(fixtureId: number, currentUser: SystemUser): Promise<BroadcastLinkResponseDto[]> {
        // Validate fixtureId
        const fixture = await this.fixtureRepository.findOneBy({ externalId: fixtureId });
        if (!fixture) {
            throw new NotFoundException(`Fixture with externalId ${fixtureId} not found`);
        }

        let whereCondition: any = { fixtureId };

        // Apply role-based filtering
        if (currentUser.role === SystemRole.EDITOR) {
            // Editor can only see broadcast links they created
            whereCondition.addedBy = currentUser.id;
        }
        // Admin and Moderator can see all broadcast links

        const broadcastLinks = await this.broadcastLinkRepository.find({ where: whereCondition });
        this.logger.debug(`Fetched ${broadcastLinks.length} broadcast links for fixture ${fixtureId} (user: ${currentUser.username}, role: ${currentUser.role})`);
        return broadcastLinks.map(link => this.mapToResponseDto(link));
    }

    /**
     * Check if user can modify (update/delete) a broadcast link
     * @param user - Current system user
     * @param broadcastLink - Broadcast link to check
     * @returns True if user can modify, false otherwise
     */
    private canUserModifyBroadcastLink(user: SystemUser, broadcastLink: BroadcastLink): boolean {
        // Admin and Moderator can modify any broadcast link
        if (user.role === SystemRole.ADMIN || user.role === SystemRole.MODERATOR) {
            return true;
        }

        // Editor can only modify broadcast links they created
        if (user.role === SystemRole.EDITOR) {
            return broadcastLink.addedBy === user.id;
        }

        return false;
    }

    /**
     * Map BroadcastLink to response DTO
     * @param broadcastLink - BroadcastLink entity
     * @returns BroadcastLinkResponseDto
     */
    /**
     * Get broadcast links for a fixture (public access - no authentication required)
     * @param fixtureId - Fixture external ID
     * @returns List of public broadcast links
     */
    async getPublicBroadcastLinksByFixtureId(fixtureId: number): Promise<PublicBroadcastLinkResponseDto[]> {
        // Validate fixtureId
        const fixture = await this.fixtureRepository.findOneBy({ externalId: fixtureId });
        if (!fixture) {
            throw new NotFoundException(`Fixture with externalId ${fixtureId} not found`);
        }

        // Get all broadcast links for the fixture (no role-based filtering for public access)
        const broadcastLinks = await this.broadcastLinkRepository.find({
            where: { fixtureId },
            order: { createdAt: 'ASC' } // Order by creation time
        });

        this.logger.debug(`Fetched ${broadcastLinks.length} public broadcast links for fixture ${fixtureId}`);
        return broadcastLinks.map(link => this.mapToPublicResponseDto(link));
    }

    private mapToResponseDto(broadcastLink: BroadcastLink): BroadcastLinkResponseDto {
        return {
            id: broadcastLink.id,
            fixtureId: broadcastLink.fixtureId,
            linkName: broadcastLink.linkName,
            linkUrl: broadcastLink.linkUrl,
            addedBy: broadcastLink.addedBy,
            linkComment: broadcastLink.linkComment,
            createdAt: broadcastLink.createdAt.toISOString(),
            updatedAt: broadcastLink.updatedAt.toISOString(),
        };
    }

    private mapToPublicResponseDto(broadcastLink: BroadcastLink): PublicBroadcastLinkResponseDto {
        return {
            id: broadcastLink.id,
            linkName: broadcastLink.linkName,
            linkUrl: broadcastLink.linkUrl,
            linkComment: broadcastLink.linkComment,
        };
    }
}