import { Injectable, Inject, Logger } from '@nestjs/common';
import { ConfigType } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';
import axios from 'axios';
import configuration from '../../core/config/configuration';

@Injectable()
export class ImageService {
    private readonly logger = new Logger(ImageService.name);
    private downloadQueue: Array<() => Promise<void>> = [];
    private isProcessingQueue = false;
    private lastRequestTime = 0;

    constructor(
        @Inject(configuration.KEY)
        private config: ConfigType<typeof configuration>,
    ) { }

    /**
     * Download an image from a URL and save it locally if it doesn't exist, with rate limiting and retry logic
     * @param url - The URL of the image to download
     * @param type - The type of image (e.g., 'leagues', 'teams', 'flags')
     * @param fileName - The name of the file to save
     * @returns The file path of the saved or existing image
     */
    async downloadImage(url: string, type: string, fileName: string): Promise<string> {
        return new Promise((resolve, reject) => {
            this.downloadQueue.push(async () => {
                try {
                    const result = await this.downloadImageInternal(url, type, fileName);
                    resolve(result);
                } catch (error) {
                    reject(error);
                }
            });
            this.processQueue();
        });
    }

    /**
     * Internal method to download image with rate limiting
     */
    private async downloadImageInternal(url: string, type: string, fileName: string): Promise<string> {
        const folderPath = path.join(this.config.imageStoragePath, type);
        const filePath = path.join(folderPath, fileName);

        // Kiểm tra file đã tồn tại
        if (fs.existsSync(filePath)) {
            return filePath;
        }

        // Tạo thư mục nếu chưa tồn tại
        fs.mkdirSync(folderPath, { recursive: true });

        // Rate limiting: Ensure minimum interval between requests
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;
        const minInterval = this.config.imageDownload.minRequestInterval;
        if (timeSinceLastRequest < minInterval) {
            const waitTime = minInterval - timeSinceLastRequest;
            this.logger.debug(`Rate limiting: waiting ${waitTime}ms before downloading ${url}`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }
        this.lastRequestTime = Date.now();

        // Enhanced retry logic with exponential backoff for rate limiting
        const maxRetries = this.config.imageDownload.maxRetries;
        let retryDelay = this.config.imageDownload.baseRetryDelay;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                const writer = fs.createWriteStream(filePath);
                const response = await axios({
                    url,
                    method: 'GET',
                    responseType: 'stream',
                    timeout: this.config.imageDownload.timeout,
                    headers: {
                        'User-Agent': 'APISportsGame/1.0',
                        'Accept': 'image/*',
                    },
                });
                response.data.pipe(writer);
                return new Promise((resolve, reject) => {
                    writer.on('finish', () => resolve(filePath));
                    writer.on('error', reject);
                });
            } catch (error) {
                const status = error.response?.status;

                if (attempt === maxRetries) {
                    this.logger.error(`Failed to download image ${url} after ${maxRetries} attempts: ${error.message}`);
                    throw new Error(`Failed to download image ${url} after ${maxRetries} attempts: ${error.message}`);
                }

                // Calculate delay based on error type
                let currentDelay = retryDelay;

                if (status === 429) {
                    // Rate limited - use exponential backoff with longer delays
                    currentDelay = Math.min(retryDelay * Math.pow(2, attempt - 1), 60000); // Max 60 seconds
                    this.logger.warn(
                        `Rate limited (HTTP 429) for ${url}. Attempt ${attempt}/${maxRetries}. Waiting ${currentDelay}ms before retry...`,
                    );
                } else if (status >= 500) {
                    // Server error - moderate delay
                    currentDelay = retryDelay * attempt;
                    this.logger.warn(
                        `Server error (HTTP ${status}) for ${url}. Attempt ${attempt}/${maxRetries}. Retrying after ${currentDelay}ms...`,
                    );
                } else {
                    // Other errors - standard delay
                    this.logger.warn(
                        `Attempt ${attempt}/${maxRetries} failed for ${url}: ${error.message}${status ? ` (HTTP ${status})` : ''}. Retrying after ${currentDelay}ms...`,
                    );
                }

                await new Promise((resolve) => setTimeout(resolve, currentDelay));
            }
        }

        throw new Error('Unexpected error in retry logic');
    }

    /**
     * Process the download queue with rate limiting
     */
    private async processQueue(): Promise<void> {
        if (this.isProcessingQueue || this.downloadQueue.length === 0) {
            return;
        }

        this.isProcessingQueue = true;

        while (this.downloadQueue.length > 0) {
            const downloadTask = this.downloadQueue.shift();
            if (downloadTask) {
                try {
                    await downloadTask();
                } catch (error) {
                    this.logger.error(`Queue processing error: ${error.message}`);
                }
            }
        }

        this.isProcessingQueue = false;
    }
}