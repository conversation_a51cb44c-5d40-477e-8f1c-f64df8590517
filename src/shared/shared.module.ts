import { Module, Global } from '@nestjs/common';
import { ImageService } from './services/image.service';
import { ImageQueueService } from './services/image-queue.service';
import { UtilsService } from './services/utils.service';

@Global()
@Module({
    providers: [ImageService, ImageQueueService, UtilsService],
    exports: [ImageService, ImageQueueService, UtilsService],
})
export class SharedModule { }