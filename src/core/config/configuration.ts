import { registerAs } from '@nestjs/config';

interface AppConfig {
    apiFootballUrl: string;
    apiFootballKey: string;
    apiRetryAttempts: number;
    apiRetryDelay: number;
    redisHost: string;
    redisPort: number;
    redisPassword: string | undefined;
    dbHost: string;
    dbUser: string;
    dbPassword: string;
    dbName: string;
    dbPort: number;
    imageStoragePath: string;
    feDomain: string;
    imageDownload: {
        minRequestInterval: number;
        maxRetries: number;
        baseRetryDelay: number;
        timeout: number;
    };
}

export const CONFIG_KEY = 'app';

export default registerAs(CONFIG_KEY, () => {
    const getEnv = (key: string, defaultValue?: string): string => {
        const value = process.env[key] ?? defaultValue;
        if (value === undefined) {
            throw new Error(`Missing required environment variable: ${key}`);
        }
        return value;
    };

    const config: AppConfig = {
        apiFootballUrl: getEnv('API_FOOTBALL_URL'),
        apiFootballKey: getEnv('API_FOOTBALL_KEY'),
        apiRetryAttempts: parseInt(getEnv('API_RETRY_ATTEMPTS', '3'), 10),
        apiRetryDelay: parseInt(getEnv('API_RETRY_DELAY', '1000'), 10),
        redisHost: getEnv('REDIS_HOST', '127.0.0.1'),
        redisPort: parseInt(getEnv('REDIS_PORT', '6379'), 10),
        redisPassword: process.env.REDIS_PASSWORD,
        dbHost: getEnv('DB_HOST', 'localhost'),
        dbUser: getEnv('DB_USER'),
        dbPassword: getEnv('DB_PASSWORD'),
        dbName: getEnv('DB_NAME'),
        dbPort: parseInt(getEnv('DB_PORT', '5432'), 10),
        imageStoragePath: getEnv('IMAGE_STORAGE_PATH', './public/images'),
        feDomain: getEnv('FE_DOMAIN'),

        // Image download rate limiting
        imageDownload: {
            minRequestInterval: parseInt(getEnv('IMAGE_DOWNLOAD_INTERVAL', '1000'), 10), // 1 second between requests
            maxRetries: parseInt(getEnv('IMAGE_DOWNLOAD_MAX_RETRIES', '5'), 10),
            baseRetryDelay: parseInt(getEnv('IMAGE_DOWNLOAD_RETRY_DELAY', '2000'), 10), // 2 seconds
            timeout: parseInt(getEnv('IMAGE_DOWNLOAD_TIMEOUT', '30000'), 10), // 30 seconds
        },
    };

    return config;
});