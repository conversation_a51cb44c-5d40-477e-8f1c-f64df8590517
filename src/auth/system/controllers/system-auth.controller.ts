import {
    Controller,
    Post,
    Body,
    HttpCode,
    HttpStatus,
    UseGuards,
    Req,
    Get,
    Put,
    Param,
    ParseIntPipe
} from '@nestjs/common';
import {
    ApiTags,
    ApiOperation,
    ApiResponse,
    ApiBody,
    ApiBearerAuth,
} from '@nestjs/swagger';
import { Request } from 'express';
import { SystemAuthService } from '../services/system-auth.service';
import {
    SystemUserLoginDto,
    SystemUserCreateDto,
    RefreshTokenDto,
    TokenPairDto,
    SystemAuthResponseDto,
    SystemUserProfileDto,
    DeviceInfoDto,
    SystemUserUpdateDto,
    SystemUserChangePasswordDto
} from '../dto/system-auth.dto';
import { SystemJwtAuthGuard } from '../guards/system-jwt-auth.guard';
import { SystemRolesGuard } from '../guards/system-roles.guard';
import { AdminOnly, GetCurrentUser } from '../../core/decorators/auth.decorators';
import { SystemUser } from '../entities/system-user.entity';
import { SystemRole } from '../../core/types/auth.types';

/**
 * System Authentication Controller
 * Handles authentication for system users (admin, editor)
 */
@ApiTags('System Authentication')
@Controller('system-auth')
export class SystemAuthController {
    constructor(private readonly systemAuthService: SystemAuthService) { }

    @ApiOperation({
        summary: 'System User Login',
        description: `
        Login endpoint for system users (admin, editor).

        **Features:**
        - Username/password authentication
        - JWT token generation
        - Device tracking
        - Audit logging
        - Refresh token support

        **Security:**
        - Rate limiting applied
        - Password validation
        - Account status verification
        - Login attempt logging
        `
    })
    @ApiBody({ type: SystemUserLoginDto })
    @ApiResponse({
        status: 200,
        description: 'Login successful',
        type: SystemAuthResponseDto
    })
    @ApiResponse({
        status: 401,
        description: 'Invalid credentials'
    })
    @ApiResponse({
        status: 429,
        description: 'Too many login attempts'
    })
    @Post('login')
    @HttpCode(HttpStatus.OK)
    async login(
        @Body() loginDto: SystemUserLoginDto,
        @Req() req: Request
    ): Promise<TokenPairDto> {
        const deviceInfo: DeviceInfoDto = {
            ipAddress: req.ip,
            userAgent: req.get('User-Agent'),
            deviceInfo: `${req.get('User-Agent')?.split(' ')[0]} on ${req.get('User-Agent')?.includes('Windows') ? 'Windows' : 'Unknown'}`
        };

        return this.systemAuthService.login(loginDto, deviceInfo);
    }

    @ApiOperation({
        summary: 'Create System User',
        description: `
        Create a new system user (admin only).

        **Features:**
        - Admin-only access
        - Role assignment
        - Password hashing
        - Email validation
        - Username uniqueness check

        **Roles:**
        - admin: Full system access
        - editor: Content management access
        `
    })
    @ApiBody({ type: SystemUserCreateDto })
    @ApiResponse({
        status: 201,
        description: 'System user created successfully',
        type: SystemUserProfileDto
    })
    @ApiResponse({
        status: 409,
        description: 'Username or email already exists'
    })
    @ApiResponse({
        status: 403,
        description: 'Admin access required'
    })
    @ApiBearerAuth()
    @UseGuards(SystemJwtAuthGuard, SystemRolesGuard)
    @AdminOnly()
    @Post('create-user')
    async createUser(@Body() createUserDto: SystemUserCreateDto): Promise<{
        message: string;
        user: SystemUserProfileDto
    }> {
        const user = await this.systemAuthService.createUser(createUserDto);

        return {
            message: 'System user created successfully',
            user: {
                id: user.id,
                username: user.username,
                email: user.email,
                fullName: user.fullName,
                role: user.role as SystemRole,
                isActive: user.isActive,
                lastLoginAt: user.lastLoginAt,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt
            }
        };
    }

    @ApiOperation({
        summary: 'Refresh Access Token',
        description: `
        Refresh an expired access token using a valid refresh token.

        **Features:**
        - Refresh token validation
        - New access token generation
        - Token expiry checking
        - User status verification
        `
    })
    @ApiBody({ type: RefreshTokenDto })
    @ApiResponse({
        status: 200,
        description: 'Access token refreshed successfully',
        schema: {
            type: 'object',
            properties: {
                accessToken: {
                    type: 'string',
                    description: 'New JWT access token'
                }
            }
        }
    })
    @ApiResponse({
        status: 401,
        description: 'Invalid or expired refresh token'
    })
    @Post('refresh')
    @HttpCode(HttpStatus.OK)
    async refreshToken(@Body() refreshTokenDto: RefreshTokenDto): Promise<{ accessToken: string }> {
        return this.systemAuthService.refreshAccessToken(refreshTokenDto.refreshToken);
    }

    @ApiOperation({
        summary: 'Logout',
        description: `
        Logout current session by revoking the refresh token.

        **Features:**
        - Refresh token revocation
        - Session termination
        - Audit logging
        `
    })
    @ApiBody({ type: RefreshTokenDto })
    @ApiResponse({
        status: 200,
        description: 'Logout successful',
        schema: {
            type: 'object',
            properties: {
                message: {
                    type: 'string',
                    example: 'Logout successful'
                }
            }
        }
    })
    @Post('logout')
    @HttpCode(HttpStatus.OK)
    async logout(@Body() refreshTokenDto: RefreshTokenDto): Promise<{ message: string }> {
        await this.systemAuthService.logout(refreshTokenDto.refreshToken);
        return { message: 'Logout successful' };
    }

    @ApiOperation({
        summary: 'Logout from All Devices',
        description: `
        Logout from all devices by revoking all refresh tokens for the current user.

        **Features:**
        - All refresh tokens revocation
        - Multi-device session termination
        - Security enhancement
        `
    })
    @ApiResponse({
        status: 200,
        description: 'Logged out from all devices successfully',
        schema: {
            type: 'object',
            properties: {
                message: {
                    type: 'string',
                    example: 'Logged out from all devices successfully'
                }
            }
        }
    })
    @ApiBearerAuth()
    @UseGuards(SystemJwtAuthGuard)
    @Post('logout-all')
    @HttpCode(HttpStatus.OK)
    async logoutFromAllDevices(@GetCurrentUser() user: SystemUser): Promise<{ message: string }> {
        await this.systemAuthService.logoutFromAllDevices(user.id);
        return { message: 'Logged out from all devices successfully' };
    }

    @ApiOperation({
        summary: 'Get Current User Profile',
        description: `
        Get the profile information of the currently authenticated system user.

        **Features:**
        - Current user information
        - Role and permissions
        - Account status
        - Last login information
        `
    })
    @ApiResponse({
        status: 200,
        description: 'User profile retrieved successfully',
        type: SystemUserProfileDto
    })
    @ApiResponse({
        status: 401,
        description: 'Authentication required'
    })
    @ApiBearerAuth()
    @UseGuards(SystemJwtAuthGuard)
    @Get('profile')
    async getProfile(@GetCurrentUser() user: SystemUser): Promise<SystemUserProfileDto> {
        return {
            id: user.id,
            username: user.username,
            email: user.email,
            fullName: user.fullName,
            role: user.role as SystemRole,
            isActive: user.isActive,
            lastLoginAt: user.lastLoginAt,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt
        };
    }

    @ApiOperation({
        summary: 'Update User Profile',
        description: `
        Update system user profile information.

        **Features:**
        - Update email and full name (all users)
        - Update role and active status (admin only)
        - Email uniqueness validation
        - Permission-based field updates

        **Permissions:**
        - All users: Can update email, fullName
        - Admin only: Can update role, isActive

        **Security:**
        - Email uniqueness check
        - Role-based access control
        - Audit logging
        `
    })
    @ApiBody({ type: SystemUserUpdateDto })
    @ApiResponse({
        status: 200,
        description: 'User profile updated successfully',
        type: SystemUserProfileDto
    })
    @ApiResponse({
        status: 403,
        description: 'Forbidden - Insufficient permissions'
    })
    @ApiResponse({
        status: 409,
        description: 'Email already exists'
    })
    @ApiBearerAuth()
    @UseGuards(SystemJwtAuthGuard, SystemRolesGuard)
    @Put('profile')
    async updateProfile(
        @Body() updateDto: SystemUserUpdateDto,
        @GetCurrentUser() currentUser: SystemUser
    ): Promise<{
        message: string;
        user: SystemUserProfileDto
    }> {
        const updatedUser = await this.systemAuthService.updateUser(currentUser.id, updateDto, currentUser);

        return {
            message: 'Profile updated successfully',
            user: {
                id: updatedUser.id,
                username: updatedUser.username,
                email: updatedUser.email,
                fullName: updatedUser.fullName,
                role: updatedUser.role as SystemRole,
                isActive: updatedUser.isActive,
                lastLoginAt: updatedUser.lastLoginAt,
                createdAt: updatedUser.createdAt,
                updatedAt: updatedUser.updatedAt
            }
        };
    }

    @ApiOperation({
        summary: 'Update User by ID (Admin Only)',
        description: `
        Update any system user profile by ID (admin only).

        **Features:**
        - Update any user's information
        - Full admin control over all fields
        - Email uniqueness validation
        - Audit logging

        **Admin Permissions:**
        - Update email, fullName, role, isActive
        - Manage any user account
        - Activate/deactivate users
        `
    })
    @ApiBody({ type: SystemUserUpdateDto })
    @ApiResponse({
        status: 200,
        description: 'User updated successfully',
        type: SystemUserProfileDto
    })
    @ApiResponse({
        status: 403,
        description: 'Admin access required'
    })
    @ApiResponse({
        status: 404,
        description: 'User not found'
    })
    @ApiResponse({
        status: 409,
        description: 'Email already exists'
    })
    @ApiBearerAuth()
    @UseGuards(SystemJwtAuthGuard, SystemRolesGuard)
    @AdminOnly()
    @Put('users/:id')
    async updateUser(
        @Param('id', ParseIntPipe) userId: number,
        @Body() updateDto: SystemUserUpdateDto,
        @GetCurrentUser() currentUser: SystemUser
    ): Promise<{
        message: string;
        user: SystemUserProfileDto
    }> {
        const updatedUser = await this.systemAuthService.updateUser(userId, updateDto, currentUser);

        return {
            message: 'User updated successfully',
            user: {
                id: updatedUser.id,
                username: updatedUser.username,
                email: updatedUser.email,
                fullName: updatedUser.fullName,
                role: updatedUser.role as SystemRole,
                isActive: updatedUser.isActive,
                lastLoginAt: updatedUser.lastLoginAt,
                createdAt: updatedUser.createdAt,
                updatedAt: updatedUser.updatedAt
            }
        };
    }

    @ApiOperation({
        summary: 'Change Password',
        description: `
        Change current user's password.

        **Features:**
        - Current password verification
        - New password confirmation
        - Password strength validation
        - Automatic logout from all devices

        **Security:**
        - Current password required
        - Password confirmation match
        - All sessions invalidated after change
        - Audit logging
        `
    })
    @ApiBody({ type: SystemUserChangePasswordDto })
    @ApiResponse({
        status: 200,
        description: 'Password changed successfully',
        schema: {
            type: 'object',
            properties: {
                message: {
                    type: 'string',
                    example: 'Password changed successfully. Please login again.'
                }
            }
        }
    })
    @ApiResponse({
        status: 400,
        description: 'Invalid password or confirmation mismatch'
    })
    @ApiBearerAuth()
    @UseGuards(SystemJwtAuthGuard)
    @Post('change-password')
    @HttpCode(HttpStatus.OK)
    async changePassword(
        @Body() changePasswordDto: SystemUserChangePasswordDto,
        @GetCurrentUser() user: SystemUser
    ): Promise<{ message: string }> {
        await this.systemAuthService.changePassword(user.id, changePasswordDto);
        return { message: 'Password changed successfully. Please login again.' };
    }
}
